<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <TargetFrameworks>net8.0;net9.0</TargetFrameworks>
    <ImplicitUsings>enable</ImplicitUsings>
    <Nullable>enable</Nullable>
    
    <!-- Package Metadata -->
    <PackageId>Daqifi.Core</PackageId>
    <Authors>DAQiFi</Authors>
    <Description>Core library for interacting with DAQiFi devices</Description>
    <PackageLicenseExpression>MIT</PackageLicenseExpression>
    <PackageProjectUrl>https://github.com/daqifi/daqifi-core</PackageProjectUrl>
    <PackageReadmeFile>README.md</PackageReadmeFile>
    <PackageTags>daqifi;daq;data-acquisition;measurement;instrumentation</PackageTags>
    <RepositoryUrl>https://github.com/daqifi/daqifi-core</RepositoryUrl>
    <RepositoryType>git</RepositoryType>
    
    <!-- Build Configuration -->
    <GenerateDocumentationFile>true</GenerateDocumentationFile>
    <IncludeSymbols>true</IncludeSymbols>
    <SymbolPackageFormat>snupkg</SymbolPackageFormat>
    <EmbedUntrackedSources>true</EmbedUntrackedSources>
    <PublishRepositoryUrl>true</PublishRepositoryUrl>
    <ContinuousIntegrationBuild Condition="'$(GITHUB_ACTIONS)' == 'true'">true</ContinuousIntegrationBuild>
  </PropertyGroup>

  <ItemGroup>
    <None Include="../../README.md" Pack="true" PackagePath="/" />
    <PackageReference Include="Google.Protobuf" Version="3.31.1" />
    <PackageReference Include="Microsoft.SourceLink.GitHub" Version="8.0.0" PrivateAssets="All"/>
    <PackageReference Include="System.IO.Ports" Version="8.0.0" />
  </ItemGroup>

</Project>
