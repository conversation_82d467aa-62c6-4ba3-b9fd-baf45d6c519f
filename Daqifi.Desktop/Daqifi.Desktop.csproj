<Project Sdk="Microsoft.NET.Sdk">

	<PropertyGroup>
		<OutputType>WinExe</OutputType>
		<TargetFramework>net8.0-windows</TargetFramework>
		<Nullable>enable</Nullable>
		<ImplicitUsings>enable</ImplicitUsings>
		<UseWPF>true</UseWPF>
		<UseWindowsForms>true</UseWindowsForms>
		<Version>3.0.0</Version>
		<InternalsVisibleTo>Daqifi.Desktop.Test</InternalsVisibleTo>
		<AllowUnsafeBlocks>true</AllowUnsafeBlocks>
	</PropertyGroup>
	<PropertyGroup>
		<ApplicationIcon>Images\WiFiDAQ.ico</ApplicationIcon>
		<AssemblyName>DAQiFi</AssemblyName>
		<ApplicationManifest>app.manifest</ApplicationManifest>
		<EnableWindowsTargeting>true</EnableWindowsTargeting>
	</PropertyGroup>
	<PropertyGroup>
		<AssemblyVersion>3.0.0.0</AssemblyVersion>
		<FileVersion>3.0.0.0</FileVersion>
	</PropertyGroup>
	<PropertyGroup Condition=" '$(Configuration)' == 'Debug' ">
	  <PlatformTarget>x64</PlatformTarget>
	</PropertyGroup>
	<PropertyGroup Condition="'$(Configuration)'=='Release'">
		<PlatformTarget>x64</PlatformTarget>
		<Prefer32Bit>false</Prefer32Bit>
	</PropertyGroup>
	<ItemGroup>
		<Compile Remove="Loggers\CsvLogger.cs" />
		<Compile Remove="ViewModels\PlottingViewModel.cs" />
		<Compile Remove="ViewModels\PlotViewModel.cs" />
		<Compile Remove="ViewModels\ViewModelLocator.cs" />
	</ItemGroup>

	<ItemGroup>
		<None Remove="Images\1Nq.png" />
		<None Remove="Images\2Nq.png" />
		<None Remove="Images\3Nq.png" />
		<None Remove="Images\Add.png" />
		<None Remove="Images\Configuration.png" />
		<None Remove="Images\DAQifi.png" />
		<None Remove="Images\Icon_Large.png" />
		<None Remove="Images\Icon_Medium.png" />
		<None Remove="Images\Icon_Small.png" />
		<None Remove="Images\Nq.png" />
		<None Remove="Images\WiFi.png" />
		<None Remove="Images\WiFiDAQ.ico" />
	</ItemGroup>
	<ItemGroup>
		<Content Include="e_sqlite3.dll">
			<CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
		</Content>
	</ItemGroup>
	<ItemGroup>
		<PackageReference Include="CommunityToolkit.Mvvm" Version="8.4.0" />
		<PackageReference Include="Daqifi.Core" Version="0.2.1" />
		<PackageReference Include="EFCore.BulkExtensions" Version="8.1.1" />
		<PackageReference Include="MahApps.Metro" Version="2.4.10" />
		<PackageReference Include="MahApps.Metro.IconPacks" Version="5.1.0" />
		<PackageReference Include="MahApps.Metro.IconPacks.Material" Version="5.1.0" />
		<PackageReference Include="Microsoft.Data.Sqlite" Version="8.0.10" />
		<PackageReference Include="Microsoft.EntityFrameworkCore" Version="8.0.10" />
		<PackageReference Include="Microsoft.EntityFrameworkCore.SqlServer" Version="8.0.10" />
		<PackageReference Include="Microsoft.EntityFrameworkCore.Tools" Version="8.0.10">
			<PrivateAssets>all</PrivateAssets>
			<IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
		</PackageReference>
		<PackageReference Include="NCalcSync" Version="5.2.11" />
		<PackageReference Include="Newtonsoft.Json" Version="13.0.3" />
		<PackageReference Include="OxyPlot.Core" Version="2.2.0" />
		<PackageReference Include="OxyPlot.Wpf" Version="2.2.0" />
		<PackageReference Include="SQLitePCLRaw.bundle_e_sqlite3" Version="2.1.10" />
		<PackageReference Include="SQLitePCLRaw.bundle_green" Version="2.1.10" />
		<PackageReference Include="System.Configuration.ConfigurationManager" Version="8.0.1" />
		<PackageReference Include="System.IO.Ports" Version="8.0.0" />
		<PackageReference Include="System.Management" Version="8.0.0" />
		<PackageReference Include="WindowsFirewallHelper" Version="2.2.0.86" />
	</ItemGroup>

	<ItemGroup>
		<ProjectReference Include="..\Daqifi.Desktop.Bootloader\Daqifi.Desktop.Bootloader.csproj" />
		<ProjectReference Include="..\Daqifi.Desktop.Common\Daqifi.Desktop.Common.csproj" />
		<ProjectReference Include="..\Daqifi.Desktop.DataModel\Daqifi.Desktop.DataModel.csproj" />
		<ProjectReference Include="..\Daqifi.Desktop.IO\Daqifi.Desktop.IO.csproj" />
	</ItemGroup>

	<ItemGroup>
		<Resource Include="Images\1Nq.png" />
		<Resource Include="Images\2Nq.png" />
		<Resource Include="Images\3Nq.png" />
		<Resource Include="Images\Add.png" />
		<Resource Include="Images\Configuration.png" />
		<Resource Include="Images\DAQifi.png" />
		<Resource Include="Images\Icon_Large.png" />
		<Resource Include="Images\Icon_Medium.png" />
		<Resource Include="Images\Icon_Small.png" />
		<Resource Include="Images\Nq.png" />
		<Resource Include="Images\WiFiDAQ.ico" />
	</ItemGroup>

</Project>
