<controls:MetroWindow x:Class="Daqifi.Desktop.MainWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:oxy="http://oxyplot.org/wpf"
        xmlns:i="http://schemas.microsoft.com/xaml/behaviors"
        xmlns:vm="clr-namespace:Daqifi.Desktop.ViewModels"
        xmlns:flyouts="clr-namespace:Daqifi.Desktop.View.Flyouts"
        xmlns:service="clr-namespace:Daqifi.Desktop.DialogService"
        xmlns:controls="clr-namespace:MahApps.Metro.Controls;assembly=MahApps.Metro"
        xmlns:iconPacks="http://metro.mahapps.com/winfx/xaml/iconpacks"
        xmlns:local="clr-namespace:Daqifi.Desktop.View"
        xmlns:converters="clr-namespace:Daqifi.Desktop.Converters"
        xmlns:logger="clr-namespace:Daqifi.Desktop.Logger"
        service:DialogService.IsRegisteredView="True"
        Height="{Binding Height, Mode=TwoWay}" Width="{Binding Width, Mode=TwoWay}" WindowState="{Binding ViewWindowState, Mode=OneWayToSource}" BorderThickness="0" GlowBrush="Black">


    <controls:MetroWindow.RightWindowCommands>
        <controls:WindowCommands>
            <Button Command="{Binding ShowDAQifiSettingsDialogCommand}">
                <Button.Content>
                    <StackPanel Orientation="Horizontal">
                        <iconPacks:PackIconMaterial Kind="Cog" Width="20" Height="20" VerticalAlignment="Center" HorizontalAlignment="Center" />
                        <TextBlock Margin="4 0 0 0" VerticalAlignment="Center" Text="settings" />
                    </StackPanel>
                </Button.Content>
            </Button>
            <Button Command="{Binding OpenHelpCommand}">
                <Button.Content>
                    <StackPanel Orientation="Horizontal">
                        <iconPacks:PackIconMaterial Kind="HelpCircleOutline" HorizontalAlignment="Center" Height="20" Width="20"/>
                        <TextBlock Margin="4 0 0 0" VerticalAlignment="Center" Text="help" />
                    </StackPanel>
                </Button.Content>
            </Button>
            <Button Command="{Binding NotificationCommand}">
                <Button.Content>
                    <Grid>
                        <iconPacks:PackIconMaterial Kind="BellOutline" VerticalAlignment="Center" HorizontalAlignment="Center" Height="20" Width="20"/>
                        <Border Background="Red" 
                       HorizontalAlignment="Right" 
                       VerticalAlignment="Top" 
                       Width="10" 
                       Height="10" 
                       CornerRadius="7.5" 
                       Margin="5 0 0 0"
                       Visibility="{Binding NotificationCount, Converter={StaticResource intToVisibility}}">
                            <TextBlock Text="{Binding NotificationCount}" 
                              VerticalAlignment="Center" 
                              HorizontalAlignment="Center" 
                              FontSize="8" 
                              FontWeight="Bold" 
                              Foreground="White"/>
                        </Border>
                    </Grid>
                </Button.Content>
            </Button>
        </controls:WindowCommands>
    </controls:MetroWindow.RightWindowCommands>

    <Window.Resources>
        <Style TargetType="{x:Type TabItem}">
            <Setter Property="Template">
                <Setter.Value>
                    <ControlTemplate TargetType="{x:Type TabItem}">
                        <Border Name="Border" Margin="0,0,-4,0" BorderBrush="White" BorderThickness="1,1,1,1">
                            <Grid>
                                <Grid Name="Rectangle" Width="5" HorizontalAlignment="Left" VerticalAlignment="Stretch" Background="#CC119EDA"/>
                                <ContentPresenter x:Name="ContentSite" VerticalAlignment="Center" HorizontalAlignment="Center" ContentSource="Header" RecognizesAccessKey="True"/>
                            </Grid>
                        </Border>
                        <ControlTemplate.Triggers>
                            <Trigger Property="IsSelected" Value="True">
                                <Setter Property="Panel.ZIndex" Value="100" />
                            </Trigger>
                            <Trigger Property="IsSelected" Value="False">
                                <Setter Property="Panel.ZIndex" Value="100" />
                                <Setter TargetName="Rectangle" Property="Visibility" Value="Collapsed"/>
                            </Trigger>
                            <MultiDataTrigger>
                                <MultiDataTrigger.Conditions>
                                    <Condition Binding="{Binding IsMouseOver, RelativeSource={RelativeSource Self}}" Value="true" />
                                    <Condition Binding="{Binding IsSelected, RelativeSource={RelativeSource Self}}" Value="false"/>
                                </MultiDataTrigger.Conditions>
                                <Setter TargetName="Rectangle" Property="Visibility" Value="Visible"/>
                            </MultiDataTrigger>
                        </ControlTemplate.Triggers>
                    </ControlTemplate>
                </Setter.Value>
            </Setter>
        </Style>

        <converters:OxyColorToBrushConverter x:Key="OxyColorToBrushConverter"/>

        <Style x:Key="TransparentButtonStyle" TargetType="Button">
            <Setter Property="Background" Value="Transparent"/>
            <Setter Property="BorderThickness" Value="0"/>
            <Setter Property="HorizontalContentAlignment" Value="Stretch"/>
            <Setter Property="VerticalContentAlignment" Value="Stretch"/>
            <Setter Property="Padding" Value="0"/>
            <Setter Property="Template">
                <Setter.Value>
                    <ControlTemplate TargetType="Button">
                        <ContentPresenter Content="{TemplateBinding Content}" />
                    </ControlTemplate>
                </Setter.Value>
            </Setter>
        </Style>

    </Window.Resources>

    <controls:MetroWindow.Flyouts>
        <controls:FlyoutsControl VerticalAlignment="Bottom">
            <!--NotificationsFlyout-->
            <flyouts:NotificationsFlyout DataContext="{Binding DataContext, RelativeSource={RelativeSource AncestorType=controls:MetroWindow}}"/>

            <!-- Live Graph Flyout -->
            <flyouts:LiveGraphFlyout DataContext="{Binding DataContext, RelativeSource={RelativeSource AncestorType=controls:MetroWindow}}"/>

            <!-- Devices Flyout-->
            <flyouts:DevicesFlyout DataContext="{Binding DataContext, RelativeSource={RelativeSource AncestorType=controls:MetroWindow}}"/>

            <!-- Channel Settings Flyout -->
            <flyouts:ChannelsFlyout DataContext="{Binding DataContext, RelativeSource={RelativeSource AncestorType=controls:MetroWindow}}"/>

            <!-- Channel Settings Flyout -->
            <flyouts:UpdateProfileFlyout DataContext="{Binding DataContext, RelativeSource={RelativeSource AncestorType=controls:MetroWindow}}"/>

            <!-- Logging Session Settings Flyout-->
            <flyouts:LoggedSessionFlyout DataContext="{Binding DataContext, RelativeSource={RelativeSource AncestorType=controls:MetroWindow}}"/>

            <!-- Summary Logger Flyout-->
            <flyouts:SummaryFlyout DataContext="{Binding DataContext, RelativeSource={RelativeSource AncestorType=controls:MetroWindow}}"/>

            <!--Firmware Updatation Flyout-->
            <flyouts:FirmwareFlyout DataContext="{Binding DataContext, RelativeSource={RelativeSource AncestorType=controls:MetroWindow}}"/>

        </controls:FlyoutsControl>
    </controls:MetroWindow.Flyouts>

    <Grid>
        <TabControl TabStripPlacement="Left" SelectedIndex="{Binding SelectedIndex}">

            <!-- Live Graph -->
            <TabItem>
                <TabItem.Header>
                    <Label>
                        <Label.Content>
                            <StackPanel Width="75" Margin="0,10,0,10">
                                <iconPacks:PackIconMaterial Kind="ChartLine" HorizontalAlignment="Center" Height="30" Width="30"/>
                                <TextBlock TextWrapping="WrapWithOverflow" Text="Live Graph" TextAlignment="Center" IsEnabled="False"/>
                            </StackPanel>
                        </Label.Content>
                    </Label>
                </TabItem.Header>
                <Grid>
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="*"/>
                        <ColumnDefinition Width="Auto"/>
                    </Grid.ColumnDefinitions>
                    <Grid.RowDefinitions>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="*"/>
                        <RowDefinition Height="Auto"/>
                    </Grid.RowDefinitions>
                    <Grid Grid.Row="0" Grid.ColumnSpan="2">
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="*"/>
                            <ColumnDefinition Width="Auto"/>
                            <ColumnDefinition Width="Auto"/>
                        </Grid.ColumnDefinitions>
                        
                        <!-- Session Status Panel -->
                        <Border Grid.Column="0"
                                Background="{DynamicResource MahApps.Brushes.Gray9}"
                                CornerRadius="4"
                                Margin="5"
                                Padding="10"
                                Visibility="{Binding IsLogging, Converter={StaticResource BoolToVis}}">
                            <Grid>
                                <Grid.ColumnDefinitions>
                                    <ColumnDefinition Width="Auto"/>
                                    <ColumnDefinition Width="*"/>
                                    <ColumnDefinition Width="Auto"/>
                                </Grid.ColumnDefinitions>
                                
                                <!-- Session Info -->
                                <StackPanel Grid.Column="0" Margin="0,0,20,0">
                                    <TextBlock Text="Active Session" FontWeight="Bold"/>
                                    <TextBlock Margin="0,5,0,0">
                                        <Run Text="Mode: "/>
                                        <Run Text="{Binding SelectedLoggingMode}" FontWeight="Bold"/>
                                    </TextBlock>
                                    <TextBlock Margin="0,5,0,0">
                                        <Run Text="Duration: "/>
                                        <Run Text="{Binding SessionDuration}" FontWeight="Bold"/>
                                    </TextBlock>
                                </StackPanel>
                                
                                <!-- Channel Info -->
                                <StackPanel Grid.Column="1">
                                    <TextBlock Text="Active Channels" FontWeight="Bold"/>
                                    <ItemsControl ItemsSource="{Binding ActiveChannels}">
                                        <ItemsControl.ItemTemplate>
                                            <DataTemplate>
                                                <TextBlock Margin="0,5,0,0">
                                                    <Run Text="{Binding Name}"/>
                                                    <Run Text=" - "/>
                                                    <Run Text="{Binding TypeString, Mode=OneWay}"/>
                                                </TextBlock>
                                            </DataTemplate>
                                        </ItemsControl.ItemTemplate>
                                    </ItemsControl>
                                </StackPanel>
                                
                                <!-- Sampling Info -->
                                <StackPanel Grid.Column="2" Margin="20,0,0,0">
                                    <TextBlock Text="Sampling" FontWeight="Bold"/>
                                    <TextBlock Margin="0,5,0,0">
                                        <Run Text="Rate: "/>
                                        <Run Text="{Binding SelectedStreamingFrequency}" FontWeight="Bold"/>
                                        <Run Text=" Hz"/>
                                    </TextBlock>
                                    <TextBlock Margin="0,5,0,0" 
                                              Visibility="{Binding IsLogToDeviceMode, Converter={StaticResource BoolToVis}}">
                                        <Run Text="Storage: "/>
                                        <Run Text="{Binding StorageUsage}" FontWeight="Bold"/>
                                    </TextBlock>
                                </StackPanel>
                            </Grid>
                        </Border>
                        
                        <controls:ToggleSwitch Grid.Column="2" 
                                              IsOn="{Binding IsLogging, Mode=TwoWay}" 
                                              IsEnabled="{Binding CanToggleLogging, Mode=OneWay}" 
                                              OffContent="Logging Off" 
                                              OnContent="Logging On" 
                                              HorizontalAlignment="Right"/>
                    </Grid>
                    <oxy:PlotView x:Name="DataLog" Grid.Row="0" Grid.RowSpan="2" Grid.Column="0" Model="{Binding Plotter.PlotModel}" Margin="5,5,20,5" TabIndex="0" />

                    <DockPanel Grid.Column="0" Grid.Row="0" HorizontalAlignment="Right" Margin="5,5,20,5">
                        <Button Command="{Binding Plotter.SaveLiveGraphCommand }" CommandParameter="{Binding}" ToolTip="Save as Image" VerticalAlignment="Bottom" Style="{StaticResource FadeButton}">
                            <Button.Content>
                                <iconPacks:PackIconMaterial Kind="ImageOutline" HorizontalAlignment="Center" Height="15" Width="15"/>
                            </Button.Content>
                        </Button>
                    </DockPanel>

                    <DockPanel Grid.Column="0" Grid.Row="1" HorizontalAlignment="Right" VerticalAlignment="Bottom">

                        <Button Command="{Binding OpenLogSummaryCommand}" ToolTip="Debug"  Style="{StaticResource FadeButton}" VerticalAlignment="Bottom">
                            <Button.Content>
                                <iconPacks:PackIconMaterial Kind="Bug" HorizontalAlignment="Center" Height="15" Width="15"/>
                            </Button.Content>
                        </Button>

                        <Button Command="{Binding Plotter.ZoomOutXCommand}" ToolTip="Zoom Out (Time)"  Style="{StaticResource FadeButton}" VerticalAlignment="Bottom">
                            <Button.Content>
                                <iconPacks:PackIconMaterial Kind="MagnifyMinusOutline" HorizontalAlignment="Center" Height="15" Width="15"/>
                            </Button.Content>
                        </Button>

                        <Button Command="{Binding Plotter.ZoomInXCommand}" ToolTip="Zoom In (Time)" Style="{StaticResource FadeButton}" VerticalAlignment="Bottom">
                            <Button.Content>
                                <iconPacks:PackIconMaterial Kind="MagnifyPlusOutline" HorizontalAlignment="Center" Height="15" Width="15"/>
                            </Button.Content>
                        </Button>

                        <Button Command="{Binding Plotter.ResetZoomLiveGraphCommand}" ToolTip="Reset Zoom" Style="{StaticResource FadeButton}" VerticalAlignment="Bottom">
                            <Button.Content>
                                <iconPacks:PackIconMaterial Kind="Crosshairs" HorizontalAlignment="Center" Height="15" Width="15"/>
                            </Button.Content>
                        </Button>

                        <StackPanel HorizontalAlignment="Right" VerticalAlignment="Bottom">
                            <Button Command="{Binding Plotter.ZoomInYCommand}" ToolTip="Zoom In (Analog)" Style="{StaticResource FadeButton}">
                                <Button.Content>
                                    <iconPacks:PackIconMaterial Kind="MagnifyPlusOutline" HorizontalAlignment="Center" Height="15" Width="15"/>
                                </Button.Content>
                            </Button>
                            <Button Command="{Binding Plotter.ZoomOutYCommand}" ToolTip="Zoom Out (Analog)" Style="{StaticResource FadeButton}">
                                <Button.Content>
                                    <iconPacks:PackIconMaterial Kind="MagnifyMinusOutline" HorizontalAlignment="Center" Height="15" Width="15"/>
                                </Button.Content>
                            </Button>
                            <Button Command="{Binding OpenLiveGraphSettingsCommand}" ToolTip="Settings" Style="{StaticResource FadeButton}">
                                <Button.Content>
                                    <iconPacks:PackIconMaterial Kind="Cog" HorizontalAlignment="Center" Height="15" Width="15"/>
                                </Button.Content>
                            </Button>

                        </StackPanel>
                    </DockPanel>

                    <ListView Grid.Row="1" Grid.Column="1" Name="ActiveChannelList" ItemsSource="{Binding ActiveInputChannels}" Background="Transparent" BorderThickness="0"
                                HorizontalContentAlignment="Stretch">
                        <ListView.ItemTemplate>
                            <DataTemplate>
                                <Button Command="{Binding DataContext.ToggleChannelVisibilityCommand, RelativeSource={RelativeSource AncestorType=ListView}}"
                                        CommandParameter="{Binding}"
                                        Style="{StaticResource TransparentButtonStyle}">
                                    <DockPanel Margin="5" Height="40" HorizontalAlignment="Left">
                                        <DockPanel.Style>
                                            <Style TargetType="DockPanel">
                                                <Setter Property="Opacity" Value="1.0"/>
                                                <Style.Triggers>
                                                    <DataTrigger Binding="{Binding IsVisible}" Value="False">
                                                        <Setter Property="Opacity" Value="0.5"/>
                                                    </DataTrigger>
                                                </Style.Triggers>
                                            </Style>
                                        </DockPanel.Style>
                                        <Rectangle Width="25" Height="25" Fill="{Binding ChannelColorBrush}"/>
                                        <StackPanel Orientation="Vertical" >
                                            <Label Content="{Binding Name}" BorderThickness="0" VerticalAlignment="Center" HorizontalAlignment="Stretch" HorizontalContentAlignment="Left"/>
                                            <Label Content="{Binding DeviceSerialNo}" 
                                                   BorderThickness="0"
                                                   FontSize="8" VerticalAlignment="Top"
                                                   HorizontalAlignment="Stretch" 
                                                   HorizontalContentAlignment="Stretch"/>
                                        </StackPanel>
                                    </DockPanel>
                                </Button>
                            </DataTemplate>
                        </ListView.ItemTemplate>
                    </ListView>
                    <Grid Grid.Row="2" Grid.Column="0" Grid.ColumnSpan="2">
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="*"/>
                            <ColumnDefinition Width="Auto"/>
                        </Grid.ColumnDefinitions>
                    </Grid>

                </Grid>
            </TabItem>

            <!-- Logged Data -->
            <TabItem>
                <TabItem.Header>
                    <Label>
                        <Label.Content>
                            <StackPanel Width="75" Margin="0,10,0,10">
                                <iconPacks:PackIconMaterial Kind="DatabaseOutline" HorizontalAlignment="Center" Height="30" Width="30"/>
                                <TextBlock TextWrapping="Wrap" Text="Logged Data" TextAlignment="Center" IsEnabled="False"/>
                            </StackPanel>
                        </Label.Content>
                    </Label>
                </TabItem.Header>
                <Grid>
                    <Grid.RowDefinitions>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="*"/>
                    </Grid.RowDefinitions>

                    <!-- Tabs for Application and Device Logs -->
                    <TabControl Grid.Row="0" Grid.RowSpan="2" Margin="5">
                        <!-- Application Logs Tab -->
                        <TabItem>
                            <TabItem.Header>
                                <TextBlock Text="APPLICATION LOGS" 
                                         FontWeight="SemiBold" 
                                         Margin="5,2"/>
                            </TabItem.Header>
                            <Grid>
                                <Grid.RowDefinitions>
                                    <RowDefinition Height="2*"/>
                                    <RowDefinition Height="*"/>
                                </Grid.RowDefinitions>

                                <!-- Logged Data Plot-->
                                <Grid Grid.Row="0">
                                    <Grid.ColumnDefinitions>
                                        <ColumnDefinition Width="*"/>      <!-- PlotView -->
                                        <ColumnDefinition Width="Auto"/>   <!-- New Legend -->
                                    </Grid.ColumnDefinitions>

                                    <oxy:PlotView Grid.Column="0" Model="{Binding DbLogger.PlotModel}" Margin="5,5,20,5" TabIndex="0"/>

                                    <ListView Grid.Column="1"
                                              ItemsSource="{Binding DbLogger.LegendItems}"
                                              Background="Transparent"
                                              BorderThickness="0"
                                              HorizontalContentAlignment="Stretch"
                                              ScrollViewer.VerticalScrollBarVisibility="Auto"
                                              MaxHeight="400"> <!-- Optional: Constrain height if needed -->
                                        <ListView.ItemTemplate>
                                            <DataTemplate DataType="{x:Type logger:LoggedSeriesLegendItem}">
                                                <Button Command="{Binding DataContext.ToggleLoggedSeriesVisibilityCommand, RelativeSource={RelativeSource AncestorType=controls:MetroWindow}}"
                                                        CommandParameter="{Binding}"
                                                        Style="{StaticResource TransparentButtonStyle}">
                                                    <DockPanel>
                                                        <DockPanel.Style>
                                                            <Style TargetType="DockPanel">
                                                                <Setter Property="Opacity" Value="1.0"/>
                                                                <Style.Triggers>
                                                                    <DataTrigger Binding="{Binding IsVisible}" Value="False">
                                                                        <Setter Property="Opacity" Value="0.5"/>
                                                                    </DataTrigger>
                                                                </Style.Triggers>
                                                            </Style>
                                                        </DockPanel.Style>
                                                        <Rectangle Width="15" Height="15" Margin="2,0,5,0" VerticalAlignment="Center">
                                                            <Rectangle.Fill>
                                                                <SolidColorBrush Color="{Binding SeriesColor, Converter={StaticResource OxyColorToBrushConverter}}"/>
                                                            </Rectangle.Fill>
                                                        </Rectangle>
                                                        <TextBlock Text="{Binding DisplayName}" VerticalAlignment="Center"/>
                                                    </DockPanel>
                                                </Button>
                                            </DataTemplate>
                                        </ListView.ItemTemplate>
                                    </ListView>
                                    
                                    <DockPanel Grid.Column="0" Grid.Row="0" HorizontalAlignment="Right" VerticalAlignment="Top">
                                        <Button Command="{Binding DbLogger.SaveGraphCommand }" CommandParameter="{Binding}" ToolTip="Save as Image" VerticalAlignment="Bottom" Style="{StaticResource FadeButton}">
                                            <iconPacks:PackIconMaterial Kind="ImageOutline" HorizontalAlignment="Center" Height="15" Width="15"/>
                                        </Button>
                                    </DockPanel>
                                    <DockPanel HorizontalAlignment="Right" VerticalAlignment="Bottom">
                                        <Button Command="{Binding DbLogger.ZoomOutXCommand}" ToolTip="Zoom Out (Time)" VerticalAlignment="Bottom" Style="{StaticResource FadeButton}">
                                            <Button.Content>
                                                <iconPacks:PackIconMaterial Kind="MagnifyMinusOutline" HorizontalAlignment="Center" Height="15" Width="15"/>
                                            </Button.Content>
                                        </Button>
                                        <Button Command="{Binding DbLogger.ZoomInXCommand}" ToolTip="Zoom In (Time)" VerticalAlignment="Bottom" Style="{StaticResource FadeButton}">
                                            <Button.Content>
                                                <iconPacks:PackIconMaterial Kind="MagnifyPlusOutline" HorizontalAlignment="Center" Height="15" Width="15"/>
                                            </Button.Content>
                                        </Button>
                                        <StackPanel>
                                            <Button Command="{Binding DbLogger.ZoomInYCommand}" ToolTip="Zoom In (Analog)" Style="{StaticResource FadeButton}">
                                                <Button.Content>
                                                    <iconPacks:PackIconMaterial Kind="MagnifyPlusOutline" HorizontalAlignment="Center" Height="15" Width="15"/>
                                                </Button.Content>
                                            </Button>
                                            <Button Command="{Binding DbLogger.ZoomOutYCommand}" ToolTip="Zoom Out (Analog)" Style="{StaticResource FadeButton}">
                                                <Button.Content>
                                                    <iconPacks:PackIconMaterial Kind="MagnifyMinusOutline" HorizontalAlignment="Center" Height="15" Width="15"/>
                                                </Button.Content>
                                            </Button>
                                            <Button Command="{Binding DbLogger.ResetZoomCommand }" CommandParameter="{Binding}" Style="{StaticResource FadeButton}" ToolTip="Reset Zoom" VerticalAlignment="Bottom" >
                                                <Button.Content>
                                                    <iconPacks:PackIconMaterial Kind="Crosshairs" HorizontalAlignment="Center" Height="15" Width="15"/>
                                                </Button.Content>
                                            </Button>
                                        </StackPanel>
                                    </DockPanel>
                                </Grid>

                                <!-- Logged Data List -->
                                <ContentPresenter Content="{Binding}" Grid.Row="1">
                                    <ContentPresenter.ContentTemplate>
                                        <DataTemplate>
                                            <Grid>
                                                <Grid.RowDefinitions>
                                                    <RowDefinition Height="*"/>
                                                    <RowDefinition Height="Auto"/>
                                                </Grid.RowDefinitions>

                                                <ListView Name="LoggingSessionList"  Grid.Row="0" ItemsSource="{Binding LoggingSessions}" Background="Transparent" BorderThickness="0">
                                                    <i:Interaction.Triggers>
                                                        <i:EventTrigger EventName="SelectionChanged">
                                                            <i:InvokeCommandAction Command="{Binding DisplayLoggingSessionCommand}" CommandParameter="{Binding SelectedItem, ElementName=LoggingSessionList}"/>
                                                        </i:EventTrigger>
                                                    </i:Interaction.Triggers>
                                                    <ListView.ItemTemplate>
                                                        <DataTemplate>
                                                            <Border>
                                                                <DockPanel >
                                                                    <TextBlock Text="{Binding Name}" DockPanel.Dock="Left" FontWeight="Bold" VerticalAlignment="Center"/>

                                                                    <!-- Delete Logging Session -->
                                                                    <Button DockPanel.Dock="Right" Command="{Binding ElementName=LoggingSessionList, Path=DataContext.DeleteLoggingSessionCommand}" CommandParameter="{Binding}" Background="#88FFFFFF" ToolTip="Delete" Padding="5">
                                                                        <Button.Content>
                                                                            <iconPacks:PackIconMaterial Kind="TrashCanOutline" HorizontalAlignment="Center" Height="20" Width="20"/>
                                                                        </Button.Content>
                                                                    </Button>

                                                                    <!-- Export Logging Session -->
                                                                    <Button DockPanel.Dock="Right" Command="{Binding ElementName=LoggingSessionList, Path=DataContext.ExportLoggingSessionCommand}" CommandParameter="{Binding}" Background="#88FFFFFF" ToolTip="Export" Padding="5">
                                                                        <Button.Content>
                                                                            <iconPacks:PackIconMaterial Kind="DownloadCircleOutline" HorizontalAlignment="Center" Height="20" Width="20"/>
                                                                        </Button.Content>
                                                                    </Button>

                                                                    <!-- Logging Session Settings -->
                                                                    <Button DockPanel.Dock="Right" Command="{Binding ElementName=LoggingSessionList, Path=DataContext.OpenLoggingSessionSettingsCommand}" CommandParameter="{Binding}" Background="#88FFFFFF" ToolTip="Settings">
                                                                        <Button.Content>
                                                                            <iconPacks:PackIconMaterial Kind="Cog" HorizontalAlignment="Center" Height="20" Width="20"/>
                                                                        </Button.Content>
                                                                    </Button>
                                                                    <Label Content="{Binding SessionStart}" DockPanel.Dock="Right" HorizontalContentAlignment="Right"/>
                                                                </DockPanel>
                                                            </Border>
                                                        </DataTemplate>
                                                    </ListView.ItemTemplate>
                                                </ListView>
                                                <DockPanel Grid.Row="1" LastChildFill="False">
                                                    <!-- Delete All Logging Session -->
                                                    <Button Name="DeleteAllLoggingSessionsButton"  DockPanel.Dock="Right" Command="{Binding ElementName=LoggingSessionList, Path=DataContext.DeleteAllLoggingSessionCommand}" Background="#88FFFFFF" ToolTip="Delete All" Padding="5">
                                                        <Button.Content>
                                                            <iconPacks:PackIconMaterial Kind="TrashCanOutline" HorizontalAlignment="Center" Height="20" Width="20"/>
                                                        </Button.Content>
                                                    </Button>

                                                    <!-- Export All Logging Session -->
                                                    <Button Name="ExportAllLoggingSessionsButton" DockPanel.Dock="Right" Command="{Binding ElementName=LoggingSessionList, Path=DataContext.ExportAllLoggingSessionCommand}" Background="#88FFFFFF" ToolTip="Export All" Padding="5">
                                                        <Button.Content>
                                                            <iconPacks:PackIconMaterial Kind="DownloadCircleOutline" HorizontalAlignment="Center" Height="20" Width="20"/>
                                                        </Button.Content>
                                                    </Button>
                                                </DockPanel>
                                                <Border Name="EmptyLoggingSessionListLabel" Visibility="Collapsed" HorizontalAlignment="Stretch" VerticalAlignment="Stretch">
                                                    <Label HorizontalAlignment="Stretch" HorizontalContentAlignment="Center" VerticalAlignment="Stretch" VerticalContentAlignment="Center">
                                                        <Label.Content>
                                                            <StackPanel Orientation="Vertical" HorizontalAlignment="Stretch" VerticalAlignment="Center">
                                                                <Label Content="No Logging Sessions Available" VerticalContentAlignment="Center" HorizontalContentAlignment="Center"/>
                                                            </StackPanel>
                                                        </Label.Content>
                                                    </Label>
                                                </Border>
                                            </Grid>
                                            <DataTemplate.Triggers>
                                                <DataTrigger Binding="{Binding LoggingSessions.Count}" Value="0">
                                                    <Setter TargetName="LoggingSessionList" Property="Visibility" Value="Collapsed"/>
                                                    <Setter TargetName="DeleteAllLoggingSessionsButton" Property="Visibility" Value="Collapsed"/>
                                                    <Setter TargetName="ExportAllLoggingSessionsButton" Property="Visibility" Value="Collapsed"/>
                                                    <Setter TargetName="EmptyLoggingSessionListLabel" Property="Visibility" Value="Visible"/>
                                                </DataTrigger>
                                            </DataTemplate.Triggers>
                                        </DataTemplate>
                                    </ContentPresenter.ContentTemplate>
                                </ContentPresenter>
                            </Grid>
                        </TabItem>

                        <!-- Device Logs Tab -->
                        <TabItem>
                            <TabItem.Header>
                                <TextBlock Text="DEVICE LOGS" 
                                         FontWeight="SemiBold" 
                                         Margin="5,2"/>
                            </TabItem.Header>
                            <local:DeviceLogsView DataContext="{Binding DeviceLogsViewModel}"/>
                        </TabItem>
                    </TabControl>

                    <!-- Loading Overlay -->
                    <Grid Grid.RowSpan="2" Visibility="{Binding IsLoggedDataBusy, FallbackValue=Hidden, Converter={StaticResource BoolToVis}}">
                        <Rectangle Fill="White" Opacity="0.75" IsHitTestVisible="False"/>
                        <StackPanel VerticalAlignment="Center">
                            <controls:ProgressRing IsActive="{Binding IsLoggedDataBusy, Mode=OneWay}"/>
                            <Label Content="{Binding LoggedDataBusyReason}" FontSize="14" HorizontalContentAlignment="Center" Foreground="#CC119EDA" FontWeight="Bold"/>
                        </StackPanel>
                    </Grid>
                </Grid>
            </TabItem>

            <!-- Channels -->
            <TabItem>
                <TabItem.Header>
                    <Label>
                        <Label.Content>
                            <StackPanel Width="75" Margin="0,10,0,10">
                                <iconPacks:PackIconMaterial Kind="HexagonMultipleOutline" HorizontalAlignment="Center" Height="30" Width="30"/>
                                <TextBlock TextWrapping="WrapWithOverflow" Text="Channels" TextAlignment="Center" IsEnabled="False"/>
                            </StackPanel>
                        </Label.Content>
                    </Label>
                </TabItem.Header>
                <ContentPresenter Content="{Binding}">
                    <ContentPresenter.ContentTemplate>
                        <DataTemplate>
                            <Grid>
                                <ListView Name="ActiveChannelList" ItemsSource="{Binding ActiveChannels}" Background="Transparent" BorderThickness="0" SelectedItem="{Binding SelectedChannel, Mode=OneWayToSource}">
                                    <ListView.ItemTemplate>
                                        <DataTemplate>
                                            <Border Height="50">
                                                <Grid>
                                                    <Grid.ColumnDefinitions>
                                                        <ColumnDefinition Width="Auto"/>
                                                        <ColumnDefinition Width="*"/>
                                                        <ColumnDefinition Width="Auto"/>
                                                        <ColumnDefinition Width="Auto"/>
                                                    </Grid.ColumnDefinitions>
                                                    <Rectangle Grid.Column="0" Width="25" Height="25" VerticalAlignment="Center" Fill="{Binding ChannelColorBrush}"/>
                                                    <StackPanel Grid.Column="1" VerticalAlignment="Center">
                                                        <Label Content="{Binding Name}" FontSize="16" Padding="5,0,0,0"/>
                                                        <Label Content="{Binding TypeString, Mode=OneWay}" Padding="5,0,0,0"/>
                                                        <Label Content="{Binding DeviceSerialNo}" FontSize="12" Padding="5,0,0,0"/>
                                                    </StackPanel>

                                                    <Button Grid.Column="2" HorizontalAlignment="Right" VerticalAlignment="Center" Command="{Binding ElementName=ActiveChannelList, Path=DataContext.OpenChannelSettingsCommand}" CommandParameter="{Binding}" ToolTip="Channel Settings">
                                                        <Button.Content>
                                                            <iconPacks:PackIconMaterial Kind="Cog" HorizontalAlignment="Center" Height="20" Width="20"/>
                                                        </Button.Content>
                                                    </Button>
                                                    <Button Grid.Column="3" HorizontalAlignment="Right" VerticalAlignment="Center" Command="{Binding ElementName=ActiveChannelList, Path=DataContext.RemoveChannelCommand}" CommandParameter="{Binding}" ToolTip="Delete Channel">
                                                        <Button.Content>
                                                            <iconPacks:PackIconMaterial Kind="TrashCanOutline" HorizontalAlignment="Center" Height="20" Width="20"/>
                                                        </Button.Content>
                                                    </Button>
                                                </Grid>
                                            </Border>
                                        </DataTemplate>
                                    </ListView.ItemTemplate>
                                </ListView>
                                <Border Name="EmptyActiveChannelListLabel" Visibility="Collapsed" HorizontalAlignment="Stretch" VerticalAlignment="Stretch">
                                    <Label HorizontalAlignment="Stretch" HorizontalContentAlignment="Center" VerticalAlignment="Stretch" VerticalContentAlignment="Center">
                                        <Label.Content>
                                            <StackPanel Orientation="Vertical" Width="150" HorizontalAlignment="Center" VerticalAlignment="Center">
                                                <Label Content="No Active Channels" VerticalContentAlignment="Center" HorizontalContentAlignment="Center"/>
                                            </StackPanel>
                                        </Label.Content>
                                    </Label>
                                </Border>

                                <!-- Add Channel Button -->
                                <Button HorizontalAlignment="Right" VerticalAlignment="Bottom" Width="50" Height="50" Command="{Binding ShowAddChannelDialogCommand}" Style="{DynamicResource MetroCircleButtonStyle}" Margin="10" Background="#CC119EDA" >
                                    <Button.Content>
                                        <iconPacks:PackIconMaterial Kind="Plus" HorizontalAlignment="Center" Height="20" Width="20" Foreground="White"/>
                                    </Button.Content>
                                    <Button.BitmapEffect>
                                        <DropShadowBitmapEffect Color="Black" Direction="270" Softness="1" ShadowDepth="15" Opacity="0.25" />
                                    </Button.BitmapEffect>
                                </Button>
                            </Grid>
                            <DataTemplate.Triggers>
                                <DataTrigger Binding="{Binding ActiveChannels.Count}" Value="0">
                                    <Setter TargetName="ActiveChannelList" Property="Visibility" Value="Collapsed"/>
                                    <Setter TargetName="EmptyActiveChannelListLabel" Property="Visibility" Value="Visible"/>
                                </DataTrigger>
                            </DataTemplate.Triggers>
                        </DataTemplate>
                    </ContentPresenter.ContentTemplate>
                </ContentPresenter>
            </TabItem>

            <!-- Devices -->
            <TabItem>
                <TabItem.Header>
                    <Label>
                        <Label.Content>
                            <StackPanel Width="75" Margin="0,10,0,10">
                                <iconPacks:PackIconMaterial Kind="Chip" HorizontalAlignment="Center" Height="30" Width="30"/>
                                <TextBlock TextWrapping="WrapWithOverflow" Text="Devices" TextAlignment="Center" IsEnabled="False"/>
                            </StackPanel>
                        </Label.Content>
                    </Label>
                </TabItem.Header>
                <ContentPresenter Content="{Binding}">
                    <ContentPresenter.ContentTemplate>
                        <DataTemplate>
                            <Grid>
                                <Grid.ColumnDefinitions>
                                    <ColumnDefinition Width="*"/>
                                    <ColumnDefinition Width="Auto"/>
                                </Grid.ColumnDefinitions>
                                <ListView Name="ConnectedDeviceList" ItemsSource="{Binding ConnectedDevices}" Background="Transparent" BorderThickness="0" Grid.Column="0" SelectedItem="{Binding SelectedDevice, Mode=TwoWay}">
                                    <ListView.ItemTemplate>
                                        <DataTemplate>
                                            <Border >
                                                <Grid>
                                                    <Grid.ColumnDefinitions>
                                                        <ColumnDefinition Width="Auto"/>
                                                        <ColumnDefinition Width="*"/>
                                                    </Grid.ColumnDefinitions>
                                                    <Image Grid.Column="0" Source="../Images/1Nq.png" Width="50" Height="50"/>
                                                    <Grid Grid.Column="1">
                                                        <Grid.RowDefinitions>
                                                            <RowDefinition Height="Auto"/>
                                                            <RowDefinition Height="Auto"/>
                                                            <RowDefinition Height="Auto"/>
                                                        </Grid.RowDefinitions>
                                                        <Grid.ColumnDefinitions>
                                                            <ColumnDefinition Width="*"/>
                                                            <ColumnDefinition Width="Auto"/>
                                                            <ColumnDefinition Width="Auto"/>
                                                            <ColumnDefinition Width="Auto"/>
                                                            <ColumnDefinition Width="Auto"/>

                                                        </Grid.ColumnDefinitions>
                                                        <StackPanel   Grid.Column="0" Grid.Row="0" Margin="5"  Orientation="Vertical">
                                                            <Label Content="{Binding Name}"  Padding="0"/>
                                                            <Label Content="{Binding IpAddress}" FontSize="10" Padding="0"/>
                                                            <Label Content="{Binding DeviceSerialNo}" FontSize="10"  Padding="0"/>
                                                            <TextBlock FontSize="10" Padding="0" Foreground="{Binding Foreground, RelativeSource={RelativeSource AncestorType=Label}}">
                                                                    <Run Text="Firmware version :" />
                                                                    <Run Text="{Binding DeviceVersion}" />
                                                            </TextBlock>

                                                        </StackPanel>
                                                        <Button Grid.RowSpan="3" Grid.Column="1" 
                                                            HorizontalAlignment="Right" VerticalAlignment="Bottom" 
                                                            Command="{Binding ElementName=ConnectedDeviceList, Path=DataContext.OpenFirmwareUpdateSettingsCommand}" 
                                                            CommandParameter="{Binding}" ToolTip="Update Firmware">

                                                            <Button.Content>
                                                                <iconPacks:PackIconMaterial Kind="Update" HorizontalAlignment="Center" Height="20" Width="20"/>
                                                            </Button.Content>
                                                        </Button>

                                                        <Button Grid.RowSpan="3" Grid.Column="2" HorizontalAlignment="Right" VerticalAlignment="Bottom" Command="{Binding ElementName=ConnectedDeviceList, Path=DataContext.OpenDeviceSettingsCommand}" CommandParameter="{Binding}" ToolTip="Device Settings">
                                                            <Button.Content>
                                                                <iconPacks:PackIconMaterial Kind="Cog" HorizontalAlignment="Center" Height="20" Width="20"/>
                                                            </Button.Content>
                                                        </Button>
                                                        <Button Grid.RowSpan="3" Grid.Column="3" HorizontalAlignment="Right" VerticalAlignment="Bottom" Command="{Binding ElementName=ConnectedDeviceList, Path=DataContext.DisconnectDeviceCommand}" CommandParameter="{Binding}" ToolTip="Disconnect From Device">
                                                            <Button.Content>
                                                                <iconPacks:PackIconMaterial Kind="TrashCanOutline" HorizontalAlignment="Center" Height="20" Width="20"/>
                                                            </Button.Content>
                                                        </Button>
                                                        <Button Grid.RowSpan="3" Grid.Column="4" HorizontalAlignment="Right" VerticalAlignment="Bottom" Command="{Binding ElementName=ConnectedDeviceList, Path=DataContext.RebootDeviceCommand}" CommandParameter="{Binding}" ToolTip="Reboot Device">
                                                            <Button.Content>
                                                                <iconPacks:PackIconMaterial Kind="Power" HorizontalAlignment="Center" Height="20" Width="20"/>
                                                            </Button.Content>
                                                        </Button>
                                                    </Grid>
                                                </Grid>
                                            </Border>
                                        </DataTemplate>
                                    </ListView.ItemTemplate>
                                </ListView>
                                <Border Name="EmptyDeviceListLabel" HorizontalAlignment="Stretch" VerticalAlignment="Stretch" Grid.Column="0" Visibility="Collapsed">
                                    <Label HorizontalAlignment="Stretch" HorizontalContentAlignment="Center" VerticalAlignment="Stretch" VerticalContentAlignment="Center" >
                                        <Label.Content>
                                            <StackPanel>
                                                <Label Content="No Devices Connected" VerticalContentAlignment="Center" HorizontalContentAlignment="Center"/>
                                            </StackPanel>
                                        </Label.Content>
                                    </Label>
                                </Border>

                                <!-- Add Device Button -->
                                <Button HorizontalAlignment="Right" VerticalAlignment="Bottom" Width="50" Height="50" Command="{Binding ShowConnectionDialogCommand}" Style="{DynamicResource MetroCircleButtonStyle}" Margin="10" Background="#CC119EDA" >
                                    <Button.Content>
                                        <iconPacks:PackIconMaterial Kind="Plus" HorizontalAlignment="Center" Height="20" Width="20 " Foreground="White"/>
                                    </Button.Content>
                                    <Button.BitmapEffect>
                                        <DropShadowBitmapEffect Color="Black" Direction="270" Softness="1" ShadowDepth="15" Opacity="0.25" />
                                    </Button.BitmapEffect>
                                </Button>
                            </Grid>
                            <DataTemplate.Triggers>
                                <DataTrigger Binding="{Binding ConnectedDevices.Count}" Value="0">
                                    <Setter TargetName="ConnectedDeviceList" Property="Visibility" Value="Collapsed"/>
                                    <Setter TargetName="EmptyDeviceListLabel" Property="Visibility" Value="Visible"/>
                                </DataTrigger>
                            </DataTemplate.Triggers>
                        </DataTemplate>
                    </ContentPresenter.ContentTemplate>
                </ContentPresenter>
            </TabItem>

            <!--profile-->
            <TabItem>
                <TabItem.Header>
                    <Label>
                        <Label.Content>
                            <StackPanel Width="75" Margin="0,10,0,10">
                                <iconPacks:PackIconMaterial Kind="FaceWomanProfile" HorizontalAlignment="Center" Height="30" Width="30"/>
                                <TextBlock TextWrapping="WrapWithOverflow" Text="Profiles" TextAlignment="Center" IsEnabled="False"/>
                            </StackPanel>
                        </Label.Content>
                    </Label>
                </TabItem.Header>
                <ContentPresenter Content="{Binding}">
                    <ContentPresenter.ContentTemplate>
                        <DataTemplate>
                            <Grid>
                                <ListView Name="ActiveProfileList" ItemsSource="{Binding profiles}" Background="Transparent" BorderThickness="0" SelectedItem="{Binding SelectedProfile, Mode=OneWayToSource}">
                                    <ListView.ItemTemplate>
                                        <DataTemplate>
                                            <Border Height="50">
                                                <Grid>
                                                    <Grid.ColumnDefinitions>
                                                        <ColumnDefinition Width="*"/>
                                                        <ColumnDefinition Width="Auto"/>
                                                        <ColumnDefinition Width="Auto"/>
                                                        <ColumnDefinition Width="Auto"/>
                                                        <ColumnDefinition Width="Auto"/>
                                                    </Grid.ColumnDefinitions>
                                                    <StackPanel Grid.Column="0" VerticalAlignment="Center" Orientation="Horizontal">
                                                        <Ellipse Width="16" Height="16" VerticalAlignment="Center" Margin="5,5,5,0">
                                                            <Ellipse.Style>
                                                                <Style TargetType="Ellipse">
                                                                    <Setter Property="Fill" Value="IndianRed"/>

                                                                    <Style.Triggers>
                                                                        <DataTrigger Binding="{Binding IsProfileActive}" Value="True">
                                                                            <Setter Property="Fill" Value="LightGreen"/>

                                                                        </DataTrigger>
                                                                    </Style.Triggers>
                                                                </Style>
                                                            </Ellipse.Style>
                                                        </Ellipse>

                                                        <!-- Name Label -->
                                                        <Label Content="{Binding Name}" FontWeight="DemiBold" FontSize="16" Padding="5,0,0,0"/>
                                                        <Label Content="{Binding CreatedOn}" FontWeight="DemiBold" Foreground="LightGray" FontSize="10" Padding="20,5,0,0"/>

                                                    </StackPanel>
                                                    <Button Grid.Column="1" HorizontalAlignment="Right" VerticalAlignment="Center" Command="{Binding ElementName=ActiveProfileList, Path=DataContext.OpenProfileSettingsCommand}" CommandParameter="{Binding}" ToolTip="Profile Settings">
                                                        <Button.Content>
                                                            <iconPacks:PackIconMaterial Kind="Cog" HorizontalAlignment="Center" Height="20" Width="20"/>
                                                        </Button.Content>
                                                    </Button>
                                                    <Button  Grid.Column="2" HorizontalAlignment="Right" VerticalAlignment="Center" Command="{Binding ElementName=ActiveProfileList, Path=DataContext.RemoveProfileCommand}" CommandParameter="{Binding}" ToolTip="Delete Profile">
                                                        <Button.Content>
                                                            <iconPacks:PackIconMaterial Kind="TrashCanOutline" HorizontalAlignment="Center" Height="20" Width="20"/>
                                                        </Button.Content>
                                                    </Button>
                                                    <Button  Grid.Column="3" HorizontalAlignment="Right" VerticalAlignment="Center" Command="{Binding ElementName=ActiveProfileList, Path=DataContext.ActivateProfileCommand}" CommandParameter="{Binding}"  ToolTip="Active Profile">
                                                        <Button.Content>
                                                            <iconPacks:PackIconMaterial Kind="CheckCircle" HorizontalAlignment="Center" Height="20" Width="20"/>
                                                        </Button.Content>
                                                    </Button>
                                                </Grid>
                                            </Border>
                                        </DataTemplate>
                                    </ListView.ItemTemplate>
                                </ListView>
                                <Border Name="EmptyActiveProfileListLabel" Visibility="Collapsed" HorizontalAlignment="Stretch" VerticalAlignment="Stretch">
                                    <Label HorizontalAlignment="Stretch" HorizontalContentAlignment="Center" VerticalAlignment="Stretch" VerticalContentAlignment="Center">
                                        <Label.Content>
                                            <StackPanel Orientation="Vertical" Width="150" HorizontalAlignment="Center" VerticalAlignment="Center">
                                                <Label Content="No Profiles Data" VerticalContentAlignment="Center" HorizontalContentAlignment="Center"/>
                                            </StackPanel>
                                        </Label.Content>
                                    </Label>
                                </Border>

                                <!-- Add profile Button -->
                                <Button HorizontalAlignment="Right" VerticalAlignment="Bottom" Width="50" Height="50" Command="{Binding ShowAddProfileConfirmationCommand}" Style="{DynamicResource MetroCircleButtonStyle}" Margin="10" Background="#CC119EDA" >
                                    <Button.Content>
                                        <iconPacks:PackIconMaterial Kind="Plus" HorizontalAlignment="Center" Height="20" Width="20" Foreground="White"/>
                                    </Button.Content>
                                    <Button.BitmapEffect>
                                        <DropShadowBitmapEffect Color="Black" Direction="270" Softness="1" ShadowDepth="15" Opacity="0.25" />
                                    </Button.BitmapEffect>
                                </Button>
                            </Grid>
                            <DataTemplate.Triggers>
                                <DataTrigger Binding="{Binding profiles.Count}" Value="0">
                                    <Setter TargetName="ActiveProfileList" Property="Visibility" Value="Collapsed"/>
                                    <Setter TargetName="EmptyActiveProfileListLabel" Property="Visibility" Value="Visible"/>
                                </DataTrigger>
                            </DataTemplate.Triggers>
                        </DataTemplate>
                    </ContentPresenter.ContentTemplate>
                </ContentPresenter>
            </TabItem>

        </TabControl>
        <Grid Background="Black" Opacity="0.5" Visibility="{Binding IsBusy, FallbackValue=Hidden, Converter={StaticResource BoolToVis}}">
            <controls:ProgressRing IsActive="{Binding IsBusy, Mode=OneWay}"/>
        </Grid>
    </Grid>
</controls:MetroWindow>