using Daqifi.Core.Communication.Consumers;
using Daqifi.Core.Communication.Messages;
using Daqifi.Desktop.IO.Messages.Consumers;
using Daqifi.Desktop.IO.Messages;

namespace Daqifi.Desktop.IO.Messages.Adapters;

/// <summary>
/// Adapter that bridges between Daqifi.Core.Communication.Consumers.IMessageConsumer<object>
/// and Daqifi.Desktop.IO.Messages.Consumers.IMessageConsumer
/// </summary>
public class CoreMessageConsumerAdapter : IMessageConsumer
{
    private readonly IMessageConsumer<object> _coreConsumer;

    public CoreMessageConsumerAdapter(IMessageConsumer<object> coreConsumer)
    {
        _coreConsumer = coreConsumer ?? throw new ArgumentNullException(nameof(coreConsumer));

        // Subscribe to core consumer events and forward them
        _coreConsumer.MessageReceived += (sender, e) =>
        {
            // CRITICAL FIX: Dispatch message handling to background thread to prevent UI freeze
            // The core library may invoke events on the main/UI thread, but desktop expects background thread processing
            Task.Run(() =>
            {
                try
                {
                    // CRITICAL FIX: Unwrap the core's ProtobufMessage to get the DaqifiOutMessage
                    // The core library provides ProtobufMessage, but desktop expects DaqifiOutMessage directly
                    if (e.Message?.Data is Daqifi.Core.Communication.Messages.ProtobufMessage coreProtobufMessage)
                    {
                        // Create a desktop-compatible ProtobufMessage that wraps the DaqifiOutMessage
                        var desktopProtobufMessage = new ProtobufMessage(coreProtobufMessage.Data);
                        var eventArgs = new MessageEventArgs<object>(desktopProtobufMessage);
                        OnMessageReceived?.Invoke(sender, eventArgs);
                    }
                    else
                    {
                        // Fallback: forward as-is (this shouldn't happen with current core implementation)
                        var eventArgs = new MessageEventArgs<object>(e.Message);
                        OnMessageReceived?.Invoke(sender, eventArgs);
                    }
                }
                catch (Exception ex)
                {
                    // Log any errors in background message processing
                    // Note: Can't use AppLogger here due to potential circular dependency
                    System.Diagnostics.Debug.WriteLine($"Error in CoreMessageConsumerAdapter background processing: {ex.Message}");
                }
            });
        };
    }

    public bool Running { get; set; }

    public Stream DataStream { get; set; } = Stream.Null;

    public event OnMessageReceivedHandler? OnMessageReceived;

    public void Start()
    {
        Running = true;
        _coreConsumer.Start();
    }

    public void Stop()
    {
        Running = false;
        _coreConsumer.Stop();
    }

    public void NotifyMessageReceived(object sender, MessageEventArgs<object> e)
    {
        OnMessageReceived?.Invoke(sender, e);
    }

    public void Run()
    {
        // Core consumer handles its own running logic
        // This method exists for compatibility with desktop interface
    }

    public void Dispose()
    {
        _coreConsumer?.Dispose();
    }
}
