using Daqifi.Core.Communication.Messages;
using Daqifi.Core.Communication.Producers;
using Daqifi.Desktop.IO.Messages.Producers;

namespace Daqifi.Desktop.IO.Messages.Adapters;

/// <summary>
/// Adapter that bridges between Daqifi.Core.Communication.Producers.IMessageProducer<string>
/// and Daqifi.Desktop.IO.Messages.Producers.IMessageProducer
/// </summary>
public class CoreMessageProducerAdapter : IMessageProducer
{
    private readonly IMessageProducer<string> _coreProducer;

    public CoreMessageProducerAdapter(IMessageProducer<string> coreProducer)
    {
        _coreProducer = coreProducer ?? throw new ArgumentNullException(nameof(coreProducer));
    }

    public void Start()
    {
        _coreProducer.Start();
    }

    public void Stop()
    {
        _coreProducer.Stop();
    }

    public void StopSafely()
    {
        // Core library doesn't have StopSafely, use regular Stop
        _coreProducer.Stop();
    }

    public void Send(IOutboundMessage<string> message)
    {
        _coreProducer.Send(message);
    }
}
