name: Build

on:
  pull_request:

jobs:
  build-and-test:
    runs-on: windows-latest

    steps:
    - uses: actions/checkout@v4

    - name: Setup .NET
      uses: actions/setup-dotnet@v4
      with:
        dotnet-version: 8.0.x

    - name: Restore dependencies
      run: dotnet restore

    - name: Build
      run: dotnet build --no-restore --configuration Release

    - name: Test
      run: dotnet test --no-build --verbosity normal --configuration Release --collect:"XPlat Code Coverage" --results-directory ./TestResults/ /p:CoverletOutputFormat=cobertura

    - name: Upload coverage reports
      uses: actions/upload-artifact@v4
      with:
        name: coverage-report
        path: ./TestResults/**/coverage.cobertura.xml
        if-no-files-found: warn # Don't fail the build if no tests are found / run

    - name: Upload coverage to Codacy
      uses: codacy/codacy-coverage-reporter-action@v1
      with:
        project-token: ${{ secrets.CODACY_PROJECT_TOKEN }}
        coverage-reports: ./TestResults/**/coverage.cobertura.xml # Or specific path if known

    - name: Build MSI Installer
      run: |
        cd Daqifi.Desktop.Setup
        dotnet build -c Release

    - name: Upload MSI Artifact
      uses: actions/upload-artifact@v4
      with:
        name: DAQifiDesktop-Installer
        path: Daqifi.Desktop.Setup/DAQifiDesktopSetup/bin/x86/Release/DAQifiDesktop_Setup.msi
        if-no-files-found: error