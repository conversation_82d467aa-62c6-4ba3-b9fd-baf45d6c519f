name: Release

on:
  release:
    types: [created]

jobs:
  build-and-release:
    runs-on: windows-latest

    steps:
    - uses: actions/checkout@v4

    - name: Setup .NET
      uses: actions/setup-dotnet@v4
      with:
        dotnet-version: 8.0.x

    - name: Restore dependencies
      run: dotnet restore

    - name: Build
      run: dotnet build --no-restore --configuration Release

    - name: Build MSI Installer
      run: |
        cd Daqifi.Desktop.Setup
        dotnet build -c Release

    - name: Upload Release Asset
      uses: softprops/action-gh-release@v2
      with:
        files: Daqifi.Desktop.Setup/DAQifiDesktopSetup/bin/x86/Release/DAQifiDesktop_Setup.msi
      env:
        GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }} 