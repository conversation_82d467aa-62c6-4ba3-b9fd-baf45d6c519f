<Project Sdk="Microsoft.NET.Sdk">
	<PropertyGroup>
		<TargetFramework>net8.0-windows</TargetFramework>
		<GenerateAssemblyInfo>false</GenerateAssemblyInfo>
		<PlatformTarget>x64</PlatformTarget>
	</PropertyGroup>
	<ItemGroup>
		<PackageReference Include="coverlet.collector" Version="6.0.4">
		  <IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
		  <PrivateAssets>all</PrivateAssets>
		</PackageReference>
		<PackageReference Include="Google.Protobuf" Version="3.30.2" />
		<PackageReference Include="Microsoft.NET.Test.Sdk" Version="17.3.0" />
		<PackageReference Include="Moq" Version="4.20.70" />
		<PackageReference Include="MSTest.TestAdapter" Version="2.2.10" />
		<PackageReference Include="MSTest.TestFramework" Version="2.2.10" />
		<PackageReference Include="protobuf-net" Version="3.1.17" />
		<PackageReference Include="System.Runtime.CompilerServices.Unsafe" Version="6.0.0" />
		<PackageReference Include="System.ValueTuple" Version="4.5.0" />
		<PackageReference Include="System.Configuration.ConfigurationManager" Version="8.0.1" />
	</ItemGroup>
	<ItemGroup>
		<ProjectReference Include="..\Daqifi.Desktop.DataModel\Daqifi.Desktop.DataModel.csproj" />
		<ProjectReference Include="..\Daqifi.Desktop.IO\Daqifi.Desktop.IO.csproj" />
		<ProjectReference Include="..\Daqifi.Desktop\Daqifi.Desktop.csproj" />
	</ItemGroup>
</Project>